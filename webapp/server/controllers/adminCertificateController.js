import { sequelize } from '../config/database.js';
import DigitalCertificate from '../models/DigitalCertificate.js';
import SignableDocument from '../models/SignableDocument.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import { verifyDigitalSignature } from '../utils/digitalSignatureUtils.js';
import { verifyDocumentOnBlockchain } from '../utils/blockchainUtils.js';

/**
 * Get all digital certificates (admin only)
 */
export const getAllCertificates = async (req, res) => {
  try {
    const certificates = await DigitalCertificate.findAll({
      attributes: {
        exclude: ['private_key', 'certificate_data', 'passphrase']
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'farm',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    return res.status(200).json({
      certificates
    });
  } catch (error) {
    console.error('Error getting all certificates:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get all documents with digital signatures (admin only)
 */
export const getDocumentsWithDigitalSignatures = async (req, res) => {
  try {
    const documents = await SignableDocument.findAll({
      where: {
        has_digital_certificate_signature: true
      },
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'signableDocumentFarm',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    return res.status(200).json({
      documents
    });
  } catch (error) {
    console.error('Error getting documents with digital signatures:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Verify a document's digital signature (admin only)
 */
export const verifyDocumentSignature = async (req, res) => {
  try {
    const { documentId } = req.params;

    // Get document
    const document = await SignableDocument.findByPk(documentId);

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    // Check if document has digital certificate signature
    if (!document.has_digital_certificate_signature) {
      return res.status(400).json({ error: 'Document does not have a digital certificate signature' });
    }

    // Verify the digital signature
    const signatureVerificationResult = await verifyDigitalSignature(document.file_path);

    // If document also has blockchain verification, verify that too
    let blockchainVerificationResult = null;
    if (document.has_blockchain_verification) {
      blockchainVerificationResult = await verifyDocumentOnBlockchain(document.id);
    }

    return res.status(200).json({
      document: {
        id: document.id,
        title: document.title,
        status: document.status
      },
      signatureVerification: signatureVerificationResult,
      blockchainVerification: blockchainVerificationResult
    });
  } catch (error) {
    console.error('Error verifying document signature:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get certificate statistics (admin only)
 */
export const getCertificateStats = async (req, res) => {
  try {
    // Get total number of certificates
    const totalCertificates = await DigitalCertificate.count();

    // Get number of active certificates
    const activeCertificates = await DigitalCertificate.count({
      where: {
        is_active: true
      }
    });

    // Get number of expired certificates
    const expiredCertificates = await DigitalCertificate.count({
      where: {
        valid_to: {
          [sequelize.Op.lt]: new Date()
        }
      }
    });

    // Get number of documents with digital signatures
    const documentsWithSignatures = await SignableDocument.count({
      where: {
        has_digital_certificate_signature: true
      }
    });

    // Get number of documents with blockchain verification
    const documentsWithBlockchain = await SignableDocument.count({
      where: {
        has_blockchain_verification: true
      }
    });

    return res.status(200).json({
      stats: {
        totalCertificates,
        activeCertificates,
        expiredCertificates,
        documentsWithSignatures,
        documentsWithBlockchain
      }
    });
  } catch (error) {
    console.error('Error getting certificate stats:', error);
    return res.status(500).json({ error: error.message });
  }
};

export default {
  getAllCertificates,
  getDocumentsWithDigitalSignatures,
  verifyDocumentSignature,
  getCertificateStats
};