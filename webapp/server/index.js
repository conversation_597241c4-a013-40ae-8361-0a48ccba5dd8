import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import cookieParser from 'cookie-parser';
import fileUpload from 'express-fileupload';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import cron from 'node-cron';
import axios from 'axios';
import * as Sentry from '@sentry/node';
import { sequelize, testConnection, initializeSchema, syncModels } from './config/database.js';
import User from './models/User.js';
import Transaction from './models/Transaction.js';
import Farm from './models/Farm.js';
import UserFarm from './models/UserFarm.js';
import { setupAssociations } from './models/associations.js';
import Product from './models/Product.js';
import InventoryCategory from './models/InventoryCategory.js';
import InventoryItem from './models/InventoryItem.js';
import InventoryTransaction from './models/InventoryTransaction.js';
import EquipmentTelematics from './models/EquipmentTelematics.js';
import IoTDevice from './models/IoTDevice.js';
import IoTData from './models/IoTData.js';
import Alert from './models/Alert.js';
import authRoutes from './routes/authRoutes.js';
import stripeFinancialConnectionsRoutes from './routes/stripeFinancialConnectionsRoutes.js';
import farmRoutes from './routes/farmRoutes.js';
import customDomainRoutes from './routes/customDomainRoutes.js';
import cropRoutes from './routes/cropRoutes.js';
import equipmentRoutes from './routes/equipmentRoutes.js';
import livestockRoutes from './routes/livestockRoutes.js';
import invoiceRoutes from './routes/invoiceRoutes.js';
import customerRoutes from './routes/customerRoutes.js';
import productRoutes from './routes/productRoutes.js';
import subscriptionRoutes from './routes/subscriptionRoutes.js';
import paymentRoutes from './routes/paymentRoutes.js';
import weatherRoutes from './routes/weatherRoutes.js';
import weatherAlertsRoutes from './routes/weatherAlertsRoutes.js';
import historicalWeatherRoutes from './routes/historicalWeatherRoutes.js';
import nceiRoutes from './routes/nceiRoutes.js';
import marketPriceRoutes from './routes/marketPriceRoutes.js';
import soilRoutes from './routes/soilRoutes.js';
import maintenanceRoutes from './routes/maintenanceRoutes.js';
import inventoryRoutes from './routes/inventoryRoutes.js';
import seedRoutes from './routes/seedRoutes.js';
import chemicalRoutes from './routes/chemicalRoutes.js';
import telematicsRoutes from './routes/telematicsRoutes.js';
import isobusRoutes from './routes/isobusRoutes.js';
import iotRoutes from './routes/iotRoutes.js';
import ambrookRoutes from './routes/ambrookRoutes.js';
import dashboardRoutes from './routes/dashboardRoutes.js';
import alertRoutes from './routes/alertRoutes.js';
import workflowRoutes from './routes/workflowRoutes.js';
import quickbooksRoutes from './routes/quickbooksRoutes.js';
import integrationRoutes from './routes/integrationRoutes.js';
import supplierRoutes from './routes/supplierRoutes.js';
import orderRoutes from './routes/orderRoutes.js';
import serviceProviderRoutes from './routes/serviceProviderRoutes.js';
import serviceRequestRoutes from './routes/serviceRequestRoutes.js';
import equipmentSharingRoutes from './routes/equipmentSharingRoutes.js';
import grantsRoutes from './routes/grantsRoutes.js';
import farmGrantsRoutes from './routes/farmGrantsRoutes.js';
import vetRoutes from './routes/vetRoutes.js';
import documentRoutes from './routes/documentRoutes.js';
import documentSigningRoutes from './routes/documentSigningRoutes.js';
import digitalCertificateRoutes from './routes/digitalCertificateRoutes.js';
import externalStorageAuthRoutes from './routes/externalStorageAuthRoutes.js';
import productInventoryRoutes from './routes/productInventoryRoutes.js';
import employeeRoutes from './routes/employeeRoutes.js';
import timeEntryRoutes from './routes/timeEntryRoutes.js';
import timeOffRequestRoutes from './routes/timeOffRequestRoutes.js';
import payStubRoutes from './routes/payStubRoutes.js';
import expenseRoutes from './routes/expenseRoutes.js';
import fieldRoutes from './routes/fieldRoutes.js';
import fieldHealthRoutes from './routes/fieldHealthRoutes.js';
import userRoutes from './routes/userRoutes.js';
import permissionRoutes from './routes/permissionRoutes.js';
import roleRoutes from './routes/roleRoutes.js';
import adminRoutes from './routes/adminRoutes.js';
import stripeDebuggerRoutes from './routes/stripeDebuggerRoutes.js';
import cronRoutes from './routes/cronRoutes.js';
import userFarmRoutes from './routes/userFarmRoutes.js';
import harvestScheduleRoutes from './routes/harvestScheduleRoutes.js';
import supportTicketRoutes from './routes/supportTicketRoutes.js';
import helpRoutes from './routes/helpRoutes.js';
import taskRoutes from './routes/taskRoutes.js';
import migrationRoutes from './routes/migrationRoutes.js';
import databaseMigrationRoutes from './routes/databaseMigrationRoutes.js';
import sentryRoutes from './routes/sentryRoutes.js';
import cropTypeRoutes from './routes/cropTypeRoutes.js';
import harvestRoutes from './routes/harvestRoutes.js';
import { router as receiptRoutes } from './routes/receiptRoutes.js';
import webhookRoutes from './routes/webhookRoutes.js';
import customerAuthRoutes from './routes/customerAuthRoutes.js';
import customerInvoiceRoutes from './routes/customerInvoiceRoutes.js';
import customerProductRoutes from './routes/customerProductRoutes.js';
import customerDeliveryRoutes from './routes/customerDeliveryRoutes.js';
import customerContactRoutes from './routes/customerContactRoutes.js';
import { processAlertRules } from './controllers/alertController.js';
import { handleErrors, notFound } from './middleware/errorMiddleware.js';
import menuPreferencesRoutes from "./routes/menuPreferencesRoutes.js";
import businessAccountRoutes from "./routes/businessAccountRoutes.js";
import driverRoutes from './routes/driverRoutes.js';
import deliveryRoutes from './routes/deliveryRoutes.js';
import pickupRoutes from './routes/pickupRoutes.js';
import driverScheduleRoutes from './routes/driverScheduleRoutes.js';
import driverLocationRoutes from './routes/driverLocationRoutes.js';
import environmentRoutes from './routes/environmentRoutes.js';
import marketRoutes from './routes/marketRoutes.js';
import sustainabilityRoutes from './routes/sustainabilityRoutes.js';
import aiAssistantRoutes from './routes/aiAssistant.js';
import aiConfigurationRoutes from './routes/aiConfigurationRoutes.js';
import aiAnalysisRoutes from './routes/aiAnalysisRoutes.js';
import aiDocumentGenerationRoutes from './routes/aiDocumentGenerationRoutes.js';
import faqRoutes from './routes/faqRoutes.js';
import sessionRoutes from './routes/sessionRoutes.js';
import scriptExecutionRoutes from './routes/scriptExecutionRoutes.js';
import reportRoutes from './routes/reportRoutes.js';
import notificationRoutes from './routes/notificationRoutes.js';
import yieldPredictionRoutes from './routes/yieldPredictionRoutes.js';
import cropDiseaseRoutes from './routes/cropDiseaseRoutes.js';
import financialAnalyticsRoutes from './routes/financialAnalyticsRoutes.js';
import cropRotationRoutes from './routes/cropRotationRoutes.js';
import databaseHealthRoutes from './routes/databaseHealthRoutes.js';
import taxManagementRoutes from './routes/taxManagementRoutes.js';
import chatRoutes from './routes/chatRoutes.js';
import matrixChatRoutes from './routes/matrixChatRoutes.js';
import initChatWebSocketServer from './websocket/chatWebSocketServer.js';

// Log loaded models
console.log('Loaded models:', Object.keys(sequelize.models));
console.log('User model imported:', !!User);
console.log('Transaction model imported:', !!Transaction);
console.log('Farm model imported:', !!Farm);
console.log('UserFarm model imported:', !!UserFarm);
console.log('Product model imported:', !!Product);
console.log('InventoryCategory model imported:', !!InventoryCategory);
console.log('InventoryItem model imported:', !!InventoryItem);
console.log('InventoryTransaction model imported:', !!InventoryTransaction);
console.log('EquipmentTelematics model imported:', !!EquipmentTelematics);
console.log('IoTDevice model imported:', !!IoTDevice);
console.log('IoTData model imported:', !!IoTData);
console.log('Alert model imported:', !!Alert);

// Load environment variables
dotenv.config();

// Initialize Sentry
Sentry.init({
  dsn: process.env.SENTRY_DSN,
  // Setting this option to true will send default PII data to Sentry.
  sendDefaultPii: true,
  // Set tracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring
  tracesSampleRate: 1.0,
  // Set environment based on NODE_ENV
  environment: process.env.NODE_ENV || 'development',
});

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Determine if we're in production
const isProduction = process.env.NODE_ENV === 'production';

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '..', 'uploads');
const tempUploadsDir = path.join(uploadsDir, 'temp');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log(`Created uploads directory: ${uploadsDir}`);
}
if (!fs.existsSync(tempUploadsDir)) {
  fs.mkdirSync(tempUploadsDir, { recursive: true });
  console.log(`Created temp uploads directory: ${tempUploadsDir}`);
}

// Middleware
// The Sentry request handler must be the first middleware
// Using setupExpressErrorHandler which sets up both request and error handlers
Sentry.setupExpressErrorHandler(app);

// Configure Helmet with custom CSP to allow Google Maps API
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'", "https://*.nxtacre.com", "https://nxtacre.com"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://*.nxtacre.com", "https://nxtacre.com", "https://*.googleapis.com"],
      connectSrc: ["'self'", "https://maps.googleapis.com", "https://*.googleapis.com", "https://*.nxtacre.com", "https://nxtacre.com", "https://o4509238970286080.ingest.us.sentry.io"],
      imgSrc: ["'self'", "data:", "https://maps.googleapis.com", "https://*.googleapis.com", "https://*.gstatic.com", "https://*.nxtacre.com", "https://nxtacre.com"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdnjs.cloudflare.com", "https://*.nxtacre.com", "https://nxtacre.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
      frameSrc: ["'self'", "https://*.nxtacre.com", "https://nxtacre.com"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
})); // Security headers with custom CSP
app.use(cors({
  origin: function(origin, callback) {
    // Allow requests with no origin (like mobile apps, curl requests)
    if (!origin) return callback(null, true);

    // Define allowed origins
    const allowedOrigins = [process.env.FRONTEND_URL || 'https://app.nxtacre.com', 'https://nxtacre.com'];

    // Check if the origin is in the allowed list
    if (allowedOrigins.indexOf(origin) !== -1) {
      return callback(null, true);
    }

    // Check if the origin is a subdomain of nxtacre.com
    if (origin.match(/^https:\/\/[a-zA-Z0-9-]+\.nxtacre\.com$/)) {
      return callback(null, true);
    }

    // Not allowed
    callback(new Error('Not allowed by CORS'));
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Cache-Control', 'Pragma', 'Expires', 'Surrogate-Control'],
  exposedHeaders: ['Set-Cookie'],
}));
app.use(morgan('dev')); // Logging
app.use(express.json()); // Parse JSON bodies
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded bodies
app.use(cookieParser()); // Parse cookies

// Set default cookie options for all cookies set by the application
app.use((req, res, next) => {
  // Store the original res.cookie function
  const originalCookie = res.cookie;

  // Override the res.cookie function to set default options
  res.cookie = function(name, value, options = {}) {
    // Set default options for all cookies
    const defaultOptions = {
      domain: '.nxtacre.com', // This will make cookies work for all subdomains
      secure: process.env.NODE_ENV === 'production', // Secure in production
      sameSite: 'none', // Allow cross-site cookies
      ...options // Allow overriding defaults
    };

    // Call the original cookie function with the new options
    return originalCookie.call(this, name, value, defaultOptions);
  };

  next();
});

// File upload middleware
app.use(fileUpload({
  useTempFiles: true,
  tempFileDir: tempUploadsDir,
  limits: { fileSize: 50 * 1024 * 1024 }, // 50MB default limit
  abortOnLimit: true,
  safeFileNames: true,
  preserveExtension: true
}));

// Add OPTIONS handling before your auth middleware
app.options('*', cors());

// Domain/subdomain handling middleware
app.use((req, res, next) => {
  const host = req.hostname;

  // Handle api.nxtacre.com - route all requests to backend endpoints
  if (host === 'api.nxtacre.com') {
    // Modify the path to add /api/ prefix if it doesn't already have it
    // This allows the api subdomain to access backend endpoints without requiring /api/ in the URL
    if (!req.path.startsWith('/api/')) {
      // Remove leading slash if present and prepend with /api/
      const newPath = '/api/' + (req.path.startsWith('/') ? req.path.substring(1) : req.path);
      req.url = newPath + (req.url.includes('?') ? req.url.substring(req.url.indexOf('?')) : '');
      console.log(`[Domain Middleware] Rewriting URL for api.nxtacre.com: ${req.path} -> ${newPath}`);
    }
  }

  // Handle redirects from root domain and www subdomain to app subdomain
  if (host === 'nxtacre.com') {
    // Preserve the path and query parameters in the redirect
    const protocol = req.secure ? 'https' : 'http';
    const redirectUrl = `${protocol}://www.nxtacre.com${req.originalUrl}`;
    return res.redirect(301, redirectUrl);
  }

  next();
});

// Serve static files in production
if (isProduction) {
  const distPath = path.join(__dirname, '..', 'dist');
  console.log(`Serving static files from: ${distPath}`);
  // Don't serve static files on api subdomain
  app.use((req, res, next) => {
    if (req.hostname === 'api.nxtacre.com') {
      return next();
    }
    express.static(distPath)(req, res, next);
  });
}

// Test database connection, initialize schema, and sync models
console.log('Testing database connection...');
testConnection()
  .then(() => {
    console.log('Database connection test completed');
    console.log('Initializing database schema...');
    return initializeSchema();
  })
  .then(() => {
    console.log('Database initialization completed');
    console.log('Synchronizing models with database...');
    return syncModels();
  })
  .then(() => {
    console.log('Setting up model associations...');
    setupAssociations();
    console.log('Model associations setup completed');
    console.log('Database setup completed successfully');
  })
  .catch(error => {
    console.error('Error during database setup:', error);
  });

// API Routes
app.get('/api/health', async (req, res) => {
  // Check if models are loaded
  const userModelLoaded = !!sequelize.models.User;
  const userModelImported = !!User;
  const transactionModelLoaded = !!sequelize.models.Transaction;
  const transactionModelImported = !!Transaction;

  let dbConnected = false;
  let usersTableExists = false;
  let transactionsTableExists = false;
  let farmsTableExists = false;
  let userFarmsTableExists = false;
  let productsTableExists = false;
  let inventoryCategoriesTableExists = false;
  let inventoryItemsTableExists = false;
  let inventoryTransactionsTableExists = false;
  let equipmentTelematicsTableExists = false;
  let iotDevicesTableExists = false;
  let iotDataTableExists = false;

  try {
    // Test database connection
    await sequelize.authenticate();
    dbConnected = true;

    const schema = process.env.DB_SCHEMA || 'site';

    // Check if users table exists
    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.users LIMIT 1`);
      usersTableExists = true;
    } catch (error) {
      console.error('Health check - Error accessing users table:', error.message);
    }

    // Check if transactions table exists
    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.transactions LIMIT 1`);
      transactionsTableExists = true;
    } catch (error) {
      console.error('Health check - Error accessing transactions table:', error.message);
    }

    // Check if farms table exists
    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.farms LIMIT 1`);
      farmsTableExists = true;
    } catch (error) {
      console.error('Health check - Error accessing farms table:', error.message);
    }

    // Check if user_farms table exists
    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.user_farms LIMIT 1`);
      userFarmsTableExists = true;
    } catch (error) {
      console.error('Health check - Error accessing user_farms table:', error.message);
    }


    // Check if products table exists
    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.products LIMIT 1`);
      productsTableExists = true;
    } catch (error) {
      console.error('Health check - Error accessing products table:', error.message);
    }


    // Check if inventory_categories table exists
    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.inventory_categories LIMIT 1`);
      inventoryCategoriesTableExists = true;
    } catch (error) {
      console.error('Health check - Error accessing inventory_categories table:', error.message);
    }

    // Check if inventory_items table exists
    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.inventory_items LIMIT 1`);
      inventoryItemsTableExists = true;
    } catch (error) {
      console.error('Health check - Error accessing inventory_items table:', error.message);
    }

    // Check if inventory_transactions table exists
    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.inventory_transactions LIMIT 1`);
      inventoryTransactionsTableExists = true;
    } catch (error) {
      console.error('Health check - Error accessing inventory_transactions table:', error.message);
    }

    // Check if equipment_telematics table exists
    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.equipment_telematics LIMIT 1`);
      equipmentTelematicsTableExists = true;
    } catch (error) {
      console.error('Health check - Error accessing equipment_telematics table:', error.message);
    }

    // Check if isobus_data table exists
    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.isobus_data LIMIT 1`);
      isobusDataTableExists = true;
    } catch (error) {
      console.error('Health check - Error accessing isobus_data table:', error.message);
    }

    // Check if iot_devices table exists
    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.iot_devices LIMIT 1`);
      iotDevicesTableExists = true;
    } catch (error) {
      console.error('Health check - Error accessing iot_devices table:', error.message);
    }

    // Check if iot_data table exists
    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.iot_data LIMIT 1`);
      iotDataTableExists = true;
    } catch (error) {
      console.error('Health check - Error accessing iot_data table:', error.message);
    }
  } catch (error) {
    console.error('Health check - Database connection error:', error.message);
  }

  res.status(200).json({
    status: 'ok',
    message: 'Server is running',
    database: {
      connected: dbConnected,
      usersTableExists: usersTableExists,
      transactionsTableExists: transactionsTableExists,
      farmsTableExists: farmsTableExists,
      userFarmsTableExists: userFarmsTableExists,
      productsTableExists: productsTableExists,
      inventoryCategoriesTableExists: inventoryCategoriesTableExists,
      inventoryItemsTableExists: inventoryItemsTableExists,
      inventoryTransactionsTableExists: inventoryTransactionsTableExists,
      equipmentTelematicsTableExists: equipmentTelematicsTableExists,
      iotDevicesTableExists: iotDevicesTableExists,
      iotDataTableExists: iotDataTableExists,
      config: {
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 5432,
        database: process.env.DB_NAME || 'farmbooks',
        user: process.env.DB_USER || 'postgres',
        schema: process.env.DB_SCHEMA || 'site'
      },
      models: {
        sequelizeModels: Object.keys(sequelize.models),
        userModelInSequelize: userModelLoaded,
        userModelImported: userModelImported,
        transactionModelInSequelize: transactionModelLoaded,
        transactionModelImported: transactionModelImported,
        farmModelInSequelize: !!sequelize.models.Farm,
        farmModelImported: !!Farm,
        userFarmModelInSequelize: !!sequelize.models.UserFarm,
        userFarmModelImported: !!UserFarm,
        productModelInSequelize: !!sequelize.models.Product,
        productModelImported: !!Product,
        inventoryCategoryModelInSequelize: !!sequelize.models.InventoryCategory,
        inventoryCategoryModelImported: !!InventoryCategory,
        inventoryItemModelInSequelize: !!sequelize.models.InventoryItem,
        inventoryItemModelImported: !!InventoryItem,
        inventoryTransactionModelInSequelize: !!sequelize.models.InventoryTransaction,
        inventoryTransactionModelImported: !!InventoryTransaction,
        equipmentTelematicsModelInSequelize: !!sequelize.models.EquipmentTelematics,
        equipmentTelematicsModelImported: !!EquipmentTelematics,
        iotDeviceModelInSequelize: !!sequelize.models.IoTDevice,
        iotDeviceModelImported: !!IoTDevice,
        iotDataModelInSequelize: !!sequelize.models.IoTData,
        iotDataModelImported: !!IoTData
      }
    }
  });
});

// Import routes
app.use('/api/auth', authRoutes);
app.use('/api/financial-connections', stripeFinancialConnectionsRoutes);
app.use('/api/farms', farmRoutes);

// Serve OpenAPI schema for Cloudflare API Shield
app.get('/api/schema', (req, res) => {
  const schemaPath = path.join(__dirname, 'docs', 'cloudflare-api-schema.yaml');
  res.sendFile(schemaPath);
});
app.use('/api/farms', customDomainRoutes);
app.use('/api/crops', cropRoutes);
app.use('/api/equipment', equipmentRoutes);
app.use('/api/livestock', livestockRoutes);
app.use('/api/invoices', invoiceRoutes);
app.use('/api/customers', customerRoutes);
app.use('/api/products', productRoutes);
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/weather', weatherRoutes);
app.use('/api/weather-alerts', weatherAlertsRoutes);
app.use('/api/historical-weather', historicalWeatherRoutes);
app.use('/api/climate', nceiRoutes);
app.use('/api/market-prices', marketPriceRoutes);
app.use('/api/soil', soilRoutes);
app.use('/api/maintenance', maintenanceRoutes);
app.use('/api/inventory', inventoryRoutes);
app.use('/api/seeds', seedRoutes);
app.use('/api/chemicals', chemicalRoutes);
app.use('/api/telematics', telematicsRoutes);
app.use('/api/isobus', isobusRoutes);
app.use('/api/iot', iotRoutes);
app.use('/api/ambrook', ambrookRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/quickbooks', quickbooksRoutes);
app.use('/api/integrations', integrationRoutes);
app.use('/api/alerts', alertRoutes);
app.use('/api/workflows', workflowRoutes);
app.use('/api/suppliers', supplierRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/service-providers', serviceProviderRoutes);
app.use('/api/service-requests', serviceRequestRoutes);
app.use('/api/equipment-sharing', equipmentSharingRoutes);
app.use('/api/grants', grantsRoutes);
app.use('/api/farm-grants', farmGrantsRoutes);
app.use('/api/vets', vetRoutes);
app.use('/api/documents', documentRoutes);
app.use('/api/document-signing', documentSigningRoutes);
app.use('/api/digital-certificates', digitalCertificateRoutes);
app.use('/api/external-storage-auth', externalStorageAuthRoutes);
app.use('/api/product-inventory', productInventoryRoutes);
app.use('/api/employees', employeeRoutes);
app.use('/api/time-entries', timeEntryRoutes);
app.use('/api/time-off-requests', timeOffRequestRoutes);
app.use('/api/pay-stubs', payStubRoutes);
app.use('/api/expenses', expenseRoutes);
app.use('/api/fields', fieldRoutes);
app.use('/api/field-health', fieldHealthRoutes);
app.use('/api/users', userRoutes);
app.use('/api/permissions', permissionRoutes);
app.use('/api/roles', roleRoutes);
app.use('/api/menu', menuPreferencesRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/admin/stripe', stripeDebuggerRoutes);
app.use('/api/business-account', businessAccountRoutes);
app.use('/api/cron', cronRoutes);
app.use('/api/user-farms', userFarmRoutes);
app.use('/api/harvest-schedules', harvestScheduleRoutes);
app.use('/api/support', supportTicketRoutes);
app.use('/api/help', helpRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/migration', migrationRoutes);
app.use('/api/db-migrations', databaseMigrationRoutes);
app.use('/api/db-health', databaseHealthRoutes);
app.use('/api/sentry', sentryRoutes);
app.use('/api/crop-types', cropTypeRoutes);
app.use('/api/harvests', harvestRoutes);
app.use('/api/receipts', receiptRoutes);
// Consolidated all webhook routes into a single file
app.use('/api/webhooks', webhookRoutes);
app.use('/api/drivers', driverRoutes);
app.use('/api/deliveries', deliveryRoutes);
app.use('/api/pickups', pickupRoutes);
app.use('/api/driver-schedules', driverScheduleRoutes);
app.use('/api/driver-locations', driverLocationRoutes);
app.use('/api/environment', environmentRoutes);
app.use('/api/market', marketRoutes);
app.use('/api/sustainability', sustainabilityRoutes);
app.use('/api/ai-assistant', aiAssistantRoutes);
app.use('/api/ai-configuration', aiConfigurationRoutes);
app.use('/api/ai-analysis', aiAnalysisRoutes);
app.use('/api/ai-document-generation', aiDocumentGenerationRoutes);
app.use('/api/faqs', faqRoutes);
app.use('/api/sessions', sessionRoutes);
app.use('/api/script-executions', scriptExecutionRoutes);
// app.use('/api/accounts', accountRoutes);
// app.use('/api/transactions', transactionRoutes);
app.use('/api/customer-auth', customerAuthRoutes);
app.use('/api/customer/invoices', customerInvoiceRoutes);
app.use('/api/customer/products', customerProductRoutes);
app.use('/api/customer/deliveries', customerDeliveryRoutes);
app.use('/api/customer/contact', customerContactRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/yield-predictions', yieldPredictionRoutes);
app.use('/api/crop-diseases', cropDiseaseRoutes);
app.use('/api/financial-analytics', financialAnalyticsRoutes);
app.use('/api/crop-rotations', cropRotationRoutes);
app.use('/api/tax', taxManagementRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/matrix-chat', matrixChatRoutes);
app.use('/api/notifications', notificationRoutes);

// Serve index.html for any non-API routes in production (for client-side routing)
if (isProduction) {
  app.get('*', (req, res, next) => {
    // Skip API routes
    if (req.path.startsWith('/api/')) {
      return next();
    }

    // Don't serve frontend routes on api subdomain
    if (req.hostname === 'api.nxtacre.com') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'The api subdomain can only be used to access API endpoints'
      });
    }

    const indexPath = path.join(__dirname, '..', 'dist', 'index.html');
    res.sendFile(indexPath, err => {
      if (err) {
        console.error(`Error sending index.html: ${err.message}`);
        next(err);
      }
    });
  });
}

// Add 404 handler (must be after all routes)
app.use(notFound);

// The Sentry error handler is already set up by setupExpressErrorHandler above
// No need to add it again here

// Add global error handler (must be last)
app.use(handleErrors);

// Set up scheduled jobs for automated alerts
// Run every hour at minute 0 (e.g., 1:00, 2:00, etc.)
cron.schedule('0 * * * *', async () => {
  console.log('Running scheduled job: Processing alert rules');

  try {
    // Get all farms
    const farms = await Farm.findAll();

    // Process alert rules for each farm
    for (const farm of farms) {
      console.log(`Processing alert rules for farm: ${farm.id}`);

      try {
        // Create a mock request and response object
        const req = {
          body: { farmId: farm.id }
        };

        const res = {
          status: (code) => ({
            json: (data) => {
              console.log(`Alert rules processed for farm ${farm.id}:`, data.message);
              if (data.results) {
                const triggeredRules = data.results.filter(r => r.triggered);
                console.log(`${triggeredRules.length} rules triggered out of ${data.results.length} total rules`);
              }
            }
          })
        };

        // Call the processAlertRules function
        await processAlertRules(req, res);
      } catch (error) {
        console.error(`Error processing alert rules for farm ${farm.id}:`, error);
      }
    }

    console.log('Finished processing alert rules for all farms');
  } catch (error) {
    console.error('Error in alert rules scheduled job:', error);
  }
});

// Set up scheduled job for AI crop rotation analysis
// Run once a week on Sunday at 2:00 AM
cron.schedule('0 2 * * 0', async () => {
  console.log('Running scheduled job: AI crop rotation analysis');

  try {
    // Import the controller
    const { runCropRotationAnalysisForAllFarms } = await import('./controllers/aiAnalysisController.js');

    // Create a mock request and response object
    const req = {};

    const res = {
      status: (code) => ({
        json: (data) => {
          console.log('AI crop rotation analysis completed:', data.message);
          console.log(`Processed ${data.results.length} farms`);

          // Log success and failure counts
          const successCount = data.results.filter(r => r.success).length;
          const failureCount = data.results.length - successCount;
          console.log(`Success: ${successCount}, Failures: ${failureCount}`);
        }
      })
    };

    // Run the analysis for all farms
    await runCropRotationAnalysisForAllFarms(req, res);

    console.log('Finished AI crop rotation analysis for all farms');
  } catch (error) {
    console.error('Error in AI crop rotation analysis scheduled job:', error);
  }
});

// Set up scheduled job for AI harvest scheduling analysis
// Run twice a month on the 1st and 15th at 3:00 AM
cron.schedule('0 3 1,15 * *', async () => {
  console.log('Running scheduled job: AI harvest scheduling analysis');

  try {
    // Import the controller
    const { runHarvestScheduleAnalysisForAllFarms } = await import('./controllers/aiAnalysisController.js');

    // Create a mock request and response object
    const req = {};

    const res = {
      status: (code) => ({
        json: (data) => {
          console.log('AI harvest scheduling analysis completed:', data.message);
          console.log(`Processed ${data.results.length} farms`);

          // Log success and failure counts
          const successCount = data.results.filter(r => r.success).length;
          const failureCount = data.results.length - successCount;
          console.log(`Success: ${successCount}, Failures: ${failureCount}`);
        }
      })
    };

    // Run the analysis for all farms
    await runHarvestScheduleAnalysisForAllFarms(req, res);

    console.log('Finished AI harvest scheduling analysis for all farms');
  } catch (error) {
    console.error('Error in AI harvest scheduling analysis scheduled job:', error);
  }
});

// Set up scheduled job for AI soil health analysis
// Run once a month on the 10th at 4:00 AM
cron.schedule('0 4 10 * *', async () => {
  console.log('Running scheduled job: AI soil health analysis');

  try {
    // Import the controller
    const { runSoilHealthAnalysisForAllFarms } = await import('./controllers/aiAnalysisController.js');

    // Create a mock request and response object
    const req = {};

    const res = {
      status: (code) => ({
        json: (data) => {
          console.log('AI soil health analysis completed:', data.message);
          console.log(`Processed ${data.results.length} farms`);

          // Log success and failure counts
          const successCount = data.results.filter(r => r.success).length;
          const failureCount = data.results.length - successCount;
          console.log(`Success: ${successCount}, Failures: ${failureCount}`);
        }
      })
    };

    // Run the analysis for all farms
    await runSoilHealthAnalysisForAllFarms(req, res);

    console.log('Finished AI soil health analysis for all farms');
  } catch (error) {
    console.error('Error in AI soil health analysis scheduled job:', error);
  }
});

// Set up scheduled job for AI field health analysis
// Run twice a month on the 5th and 20th at 5:00 AM
cron.schedule('0 5 5,20 * *', async () => {
  console.log('Running scheduled job: AI field health analysis');

  try {
    // Import the controller
    const { runFieldHealthAnalysisForAllFarms } = await import('./controllers/aiAnalysisController.js');

    // Create a mock request and response object
    const req = {};

    const res = {
      status: (code) => ({
        json: (data) => {
          console.log('AI field health analysis completed:', data.message);
          console.log(`Processed ${data.results.length} farms`);

          // Log success and failure counts
          const successCount = data.results.filter(r => r.success).length;
          const failureCount = data.results.length - successCount;
          console.log(`Success: ${successCount}, Failures: ${failureCount}`);
        }
      })
    };

    // Run the analysis for all farms
    await runFieldHealthAnalysisForAllFarms(req, res);

    console.log('Finished AI field health analysis for all farms');
  } catch (error) {
    console.error('Error in AI field health analysis scheduled job:', error);
  }
});

// Set up scheduled job for AI herd health analysis
// Run once a month on the 15th at 6:00 AM
cron.schedule('0 6 15 * *', async () => {
  console.log('Running scheduled job: AI herd health analysis');

  try {
    // Import the controller
    const { runHerdHealthAnalysisForAllFarms } = await import('./controllers/aiAnalysisController.js');

    // Create a mock request and response object
    const req = {};

    const res = {
      status: (code) => ({
        json: (data) => {
          console.log('AI herd health analysis completed:', data.message);
          console.log(`Processed ${data.results.length} farms`);

          // Log success and failure counts
          const successCount = data.results.filter(r => r.success).length;
          const failureCount = data.results.length - successCount;
          console.log(`Success: ${successCount}, Failures: ${failureCount}`);
        }
      })
    };

    // Run the analysis for all farms
    await runHerdHealthAnalysisForAllFarms(req, res);

    console.log('Finished AI herd health analysis for all farms');
  } catch (error) {
    console.error('Error in AI herd health analysis scheduled job:', error);
  }
});

// Set up scheduled job for Matrix user synchronization
// Run daily at 3:00 AM
cron.schedule('0 3 * * *', async () => {
  console.log('Running scheduled job: Matrix user synchronization');

  try {
    // Import the synchronization script
    const syncUsersToMatrix = (await import('./scripts/syncUsersToMatrix.js')).default;

    // Run the synchronization
    await syncUsersToMatrix();

    console.log('Finished Matrix user synchronization');
  } catch (error) {
    console.error('Error in Matrix user synchronization scheduled job:', error);
  }
});

// Set up scheduled job for Matrix conversation migration
// Run every 6 hours
cron.schedule('0 */6 * * *', async () => {
  console.log('Running scheduled job: Matrix conversation migration');

  try {
    // Import the migration script
    const migrateConversationsToMatrix = (await import('./scripts/migrateConversationsToMatrix.js')).default;

    // Run the migration
    await migrateConversationsToMatrix();

    console.log('Finished Matrix conversation migration');
  } catch (error) {
    console.error('Error in Matrix conversation migration scheduled job:', error);
  }
});

// Start the server
import http from 'http';

const server = http.createServer(app);

// Initialize WebSocket server for chat
const chatWs = initChatWebSocketServer(server);
console.log('Chat WebSocket server initialized');

// Set up graceful shutdown handlers
process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);
  gracefulShutdown();
});

// Flag to indicate server is shutting down
let isShuttingDown = false;

// Export the shutdown status for other modules to check
export const isServerShuttingDown = () => isShuttingDown;

// Graceful shutdown function
function gracefulShutdown() {
  // Set the shutdown flag to true
  isShuttingDown = true;
  console.log('Received shutdown signal. Closing server...');

  // Stop accepting new connections
  console.log('Stopping new connections...');

  // Close the WebSocket server first
  if (chatWs && typeof chatWs.close === 'function') {
    console.log('Closing chat WebSocket server...');
    chatWs.close();
  }

  // Give active connections some time to finish
  console.log('Waiting for active connections to finish...');
  setTimeout(() => {
    // Close the HTTP server
    server.close(() => {
      console.log('HTTP server closed.');

      // Wait a bit more for any pending database operations
      setTimeout(() => {
        // Close database connection
        console.log('Closing database connection...');
        sequelize.close()
          .then(() => {
            console.log('Database connection closed.');
            process.exit(0);
          })
          .catch((err) => {
            console.error('Error closing database connection:', err);
            process.exit(1);
          });
      }, 1000); // Wait 1 second for pending database operations
    });
  }, 2000); // Wait 2 seconds for active connections

  // Force exit after 15 seconds if graceful shutdown fails
  setTimeout(() => {
    console.error('Forced shutdown after timeout');
    process.exit(1);
  }, 15000);
}

server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API available at http://localhost:${PORT}/api`);
  console.log(`Chat WebSocket available at ws://localhost:${PORT}/ws/chat`);
});

export default app;
