import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import User from './User.js';
import Farm from './Farm.js';

const DigitalCertificate = defineModel('DigitalCertificate', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  certificate_name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  certificate_data: {
    type: DataTypes.BLOB,
    allowNull: false
  },
  private_key: {
    type: DataTypes.BLOB,
    allowNull: false
  },
  passphrase: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  certificate_type: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  issuer: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  subject: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  valid_from: {
    type: DataTypes.DATE,
    allowNull: false
  },
  valid_to: {
    type: DataTypes.DATE,
    allowNull: false
  },
  serial_number: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'digital_certificates',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'digital_certificates_farm_id_idx',
      fields: ['farm_id']
    },
    {
      name: 'digital_certificates_user_id_idx',
      fields: ['user_id']
    },
    {
      name: 'digital_certificates_is_active_idx',
      fields: ['is_active']
    }
  ]
});

// Define associations
DigitalCertificate.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
DigitalCertificate.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });

export default DigitalCertificate;