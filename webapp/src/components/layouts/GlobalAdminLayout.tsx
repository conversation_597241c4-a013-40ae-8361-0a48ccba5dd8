import React, { useEffect, useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';

interface GlobalAdminLayoutProps {
  children: React.ReactNode;
}

// NavGroup component for collapsible sidebar sections
interface NavGroupProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
  isActive: boolean;
}

const NavGroup = ({ title, children, defaultOpen = false, isActive }: NavGroupProps) => {
  const [isOpen, setIsOpen] = useState(defaultOpen || isActive);

  return (
    <div className="mb-2">
      <button
        className={`w-full flex items-center px-4 py-3 text-left ${
          isActive ? 'bg-primary-800 dark:bg-gray-800 text-white' : 'text-primary-200 hover:bg-primary-600 dark:hover:bg-gray-700'
        }`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="flex-1">{title}</span>
        {isOpen ? (
          <ChevronDownIcon className="h-4 w-4" />
        ) : (
          <ChevronRightIcon className="h-4 w-4" />
        )}
      </button>
      {isOpen && (
        <div className="ml-4 pl-2 border-l border-primary-600 dark:border-gray-600">
          {children}
        </div>
      )}
    </div>
  );
};

const GlobalAdminLayout: React.FC<GlobalAdminLayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Check if user is a global admin
  useEffect(() => {
    // If user is not logged in or not a global admin, redirect to unauthorized page
    if (!user || user.is_global_admin !== true) {
      navigate('/unauthorized');
    }
  }, [user, navigate]);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path);
  };

  // Check if any path in a group is active
  const isGroupActive = (paths: string[]) => {
    return paths.some(path => 
      location.pathname === path || location.pathname.startsWith(path)
    );
  };

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-800 overflow-hidden">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-20 bg-black bg-opacity-50 transition-opacity lg:hidden"
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 dark:bg-gray-900 text-white transform transition duration-300 lg:translate-x-0 lg:static lg:inset-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="p-4 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">nxtAcre Admin</h1>
            <p className="text-primary-200 text-sm">Global Administration</p>
          </div>
          <button 
            className="text-white lg:hidden"
            onClick={() => setSidebarOpen(false)}
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
        <nav className="mt-8 overflow-y-auto max-h-[calc(100vh-200px)]">
          <ul>
            {/* Dashboard & Overview */}
            <li className="mb-2">
              <Link
                to="/admin"
                className={`flex items-center px-4 py-3 ${
                  isActive('/admin') && !isActive('/admin/subscriptions') && !isActive('/admin/farms') && !isActive('/admin/users') && !isActive('/admin/integrations') && !isActive('/admin/suppliers')
                    ? 'bg-primary-800 dark:bg-gray-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600 dark:hover:bg-gray-700'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                Dashboard
              </Link>
            </li>

            {/* User & Access Management */}
            <NavGroup 
              title="User & Access Management" 
              isActive={isGroupActive(['/admin/users', '/admin/roles', '/admin/subscriptions'])}
              defaultOpen={true}
            >
              <Link
                to="/admin/users"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/users')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
                User Management
              </Link>
              <Link
                to="/admin/roles"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/roles')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                Role Management
              </Link>
              <Link
                to="/admin/subscriptions"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/subscriptions')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Subscription Management
              </Link>
            </NavGroup>

            {/* Content Management */}
            <NavGroup 
              title="Content Management" 
              isActive={isGroupActive(['/admin/farms', '/admin/suppliers', '/admin/dashboard-layouts'])}
              defaultOpen={false}
            >
              <Link
                to="/admin/farms"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/farms')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                Farm Management
              </Link>
              <Link
                to="/admin/suppliers"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/suppliers')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                Supplier Management
              </Link>
              <Link
                to="/admin/dashboard-layouts"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/dashboard-layouts')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                </svg>
                Dashboard Layouts
              </Link>
            </NavGroup>

            {/* System Configuration */}
            <NavGroup 
              title="System Configuration" 
              isActive={isGroupActive(['/admin/integrations', '/admin/environment', '/admin/db-migrations', '/admin/db-health', '/admin/ai-configuration'])}
              defaultOpen={false}
            >
              <Link
                to="/admin/integrations"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/integrations') || isActive('/admin/api-endpoints') || isActive('/admin/api-cache')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"></path>
                </svg>
                Integration Management
              </Link>
              <Link
                to="/admin/environment"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/environment')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                Environment Variables
              </Link>
              <Link
                to="/admin/db-migrations"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/db-migrations')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
                Database Migrations
              </Link>
              <Link
                to="/admin/db-health"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/db-health')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                Database Health
              </Link>
              <Link
                to="/admin/scripts"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/scripts')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                Script Executions
              </Link>
              <Link
                to="/admin/api-endpoints"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/api-endpoints')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                API Endpoints
              </Link>
              <Link
                to="/admin/api-cache"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/api-cache')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                </svg>
                API Cache
              </Link>
              <Link
                to="/admin/ai-configuration"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/ai-configuration')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                AI Configuration
              </Link>
            </NavGroup>

            {/* Support & Help */}
            <NavGroup 
              title="Support & Help" 
              isActive={isGroupActive(['/admin/help-tips', '/admin/help-guides', '/admin/support-tickets', '/admin/faqs'])}
              defaultOpen={false}
            >
              <Link
                to="/admin/help-tips"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/help-tips')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Help Tips
              </Link>
              <Link
                to="/admin/help-guides"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/help-guides')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
                Help Guides
              </Link>
              <Link
                to="/admin/support-tickets"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/support-tickets')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                </svg>
                Support Tickets
              </Link>
              <Link
                to="/admin/faqs"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/faqs')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                FAQ Management
              </Link>
            </NavGroup>

            {/* Monitoring & Logs */}
            <NavGroup 
              title="Monitoring & Logs" 
              isActive={isGroupActive(['/admin/logs'])}
              defaultOpen={false}
            >
              <Link
                to="/admin/logs"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/logs')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
                Error Logs
              </Link>
            </NavGroup>

            {/* Security & Verification */}
            <NavGroup 
              title="Security & Verification" 
              isActive={isGroupActive(['/admin/certificate-verifier', '/admin/stripe-debugger'])}
              defaultOpen={false}
            >
              <Link
                to="/admin/certificate-verifier"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/certificate-verifier')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                Certificate Verifier
              </Link>
              <Link
                to="/admin/stripe-debugger"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/stripe-debugger')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Stripe Debugger
              </Link>
            </NavGroup>

            {/* Matrix Chat Administration */}
            <NavGroup 
              title="Matrix Chat Administration" 
              isActive={isGroupActive(['/admin/matrix-users', '/admin/matrix-rooms', '/admin/matrix-stats'])}
              defaultOpen={false}
            >
              <Link
                to="/admin/matrix-users"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/matrix-users')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
                Matrix Users
              </Link>
              <Link
                to="/admin/matrix-rooms"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/matrix-rooms')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                Matrix Rooms
              </Link>
              <Link
                to="/admin/matrix-stats"
                className={`flex items-center px-4 py-2 mb-2 ${
                  isActive('/admin/matrix-stats')
                    ? 'bg-primary-800 text-white'
                    : 'text-primary-200 hover:bg-primary-600'
                }`}
              >
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                Matrix Server Stats
              </Link>
            </NavGroup>
          </ul>
        </nav>
        <div className="absolute bottom-0 w-64 p-4">
          <Link to="/dashboard" className="flex items-center text-primary-200 hover:text-white mb-4">
            <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
            </svg>
            Back to App
          </Link>
          <button
            onClick={handleLogout}
            className="flex items-center text-primary-200 hover:text-white"
          >
            <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            Logout
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <header className="bg-white shadow">
          <div className="px-6 py-4 flex items-center">
            {/* Hamburger menu button - visible only on small screens */}
            <button
              type="button"
              className="mr-4 text-gray-500 lg:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <span className="sr-only">Open sidebar</span>
              <Bars3Icon className="h-6 w-6" />
            </button>
            <h2 className="text-xl font-semibold text-gray-800">
              {location.pathname === '/admin' && 'Dashboard'}
              {location.pathname === '/admin/farms' && 'Farm Management'}
              {location.pathname === '/admin/users' && 'User Management'}
              {location.pathname === '/admin/roles' && 'Role Management'}
              {location.pathname === '/admin/subscriptions' && 'Subscription Management'}
              {location.pathname === '/admin/integrations' && 'Integration Management'}
              {location.pathname === '/admin/suppliers' && 'Supplier Management'}
              {location.pathname === '/admin/logs' && 'Error Logs'}
              {location.pathname === '/admin/environment' && 'Environment Variables'}
              {location.pathname === '/admin/db-migrations' && 'Database Migrations'}
              {location.pathname === '/admin/db-health' && 'Database Health'}
              {location.pathname === '/admin/scripts' && 'Script Executions'}
              {location.pathname === '/admin/api-endpoints' && 'API Endpoints Management'}
              {location.pathname === '/admin/api-cache' && 'API Cache Management'}
              {location.pathname === '/admin/help-tips' && 'Help Tips Management'}
              {location.pathname === '/admin/help-guides' && 'Help Guides Management'}
              {location.pathname === '/admin/support-tickets' && 'Support Ticket Management'}
              {location.pathname === '/admin/dashboard-layouts' && 'Dashboard Layouts Management'}
              {location.pathname === '/admin/ai-configuration' && 'AI Configuration'}
              {location.pathname === '/admin/matrix-users' && 'Matrix User Management'}
              {location.pathname === '/admin/matrix-rooms' && 'Matrix Room Management'}
              {location.pathname === '/admin/matrix-stats' && 'Matrix Server Statistics'}
              {location.pathname === '/admin/certificate-verifier' && 'Certificate Verification'}
              {location.pathname === '/admin/stripe-debugger' && 'Stripe Debugger'}
            </h2>
          </div>
        </header>
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default GlobalAdminLayout;
