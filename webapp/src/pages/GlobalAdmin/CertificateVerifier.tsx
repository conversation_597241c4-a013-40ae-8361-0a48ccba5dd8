import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';

interface Certificate {
  id: string;
  certificate_name: string;
  issuer: string;
  subject: string;
  valid_from: string;
  valid_to: string;
  is_active: boolean;
  farm: {
    id: string;
    name: string;
  };
  user: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

interface Document {
  id: string;
  title: string;
  document_type: string;
  status: string;
  file_path: string;
  has_digital_certificate_signature: boolean;
  has_blockchain_verification: boolean;
  farm_id: string;
  created_at: string;
}

interface VerificationResult {
  verified: boolean;
  documentHash?: string;
  message: string;
  error?: string;
  blockchainVerified?: boolean;
  blockchainMessage?: string;
}

const CertificateVerifier: React.FC = () => {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [verifying, setVerifying] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [farmFilter, setFarmFilter] = useState<string>('');
  const [farms, setFarms] = useState<{id: string, name: string}[]>([]);
  const [stats, setStats] = useState<{
    totalCertificates: number;
    activeCertificates: number;
    expiredCertificates: number;
    documentsWithSignatures: number;
    documentsWithBlockchain: number;
  }>({
    totalCertificates: 0,
    activeCertificates: 0,
    expiredCertificates: 0,
    documentsWithSignatures: 0,
    documentsWithBlockchain: 0
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all farms for filtering
      const farmsResponse = await axios.get(`${API_URL}/admin/farms`);
      setFarms(farmsResponse.data.farms || []);

      // Fetch certificate stats
      const statsResponse = await axios.get(`${API_URL}/admin/certificate-stats`);
      setStats(statsResponse.data.stats);

      // Fetch all certificates
      const certificatesResponse = await axios.get(`${API_URL}/admin/digital-certificates`);
      setCertificates(certificatesResponse.data.certificates || []);

      // Fetch all documents with digital signatures
      const documentsResponse = await axios.get(`${API_URL}/admin/signable-documents/with-digital-signatures`);
      setDocuments(documentsResponse.data.documents || []);

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching data:', err);
      setError(err.response?.data?.error || 'Failed to load data');
      setLoading(false);
    }
  };

  const handleVerifyDocument = async (documentId: string) => {
    try {
      setVerifying(true);
      setError(null);
      setSuccessMessage(null);
      setVerificationResult(null);

      // Find the document
      const document = documents.find(doc => doc.id === documentId);
      if (!document) {
        throw new Error('Document not found');
      }

      setSelectedDocument(document);

      // Verify document signature using admin endpoint
      const verificationResponse = await axios.get(`${API_URL}/admin/signable-documents/${documentId}/verify-signature`);

      const signatureVerification = verificationResponse.data.signatureVerification;
      const blockchainVerification = verificationResponse.data.blockchainVerification;

      // Set verification result
      setVerificationResult({
        verified: signatureVerification.verified,
        documentHash: signatureVerification.documentHash,
        message: signatureVerification.message,
        blockchainVerified: blockchainVerification?.verified,
        blockchainMessage: blockchainVerification?.message
      });

      if (signatureVerification.verified) {
        setSuccessMessage(`Document "${document.title}" has been verified successfully.`);
      } else {
        setError(`Document verification failed: ${signatureVerification.message}`);
      }

      setVerifying(false);
    } catch (err: any) {
      console.error('Error verifying document:', err);
      setError(err.response?.data?.error || 'Failed to verify document');
      setVerifying(false);
    }
  };

  // Filter documents based on search term and farm filter
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = searchTerm === '' || 
      doc.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFarm = farmFilter === '' || doc.farm_id === farmFilter;
    return matchesSearch && matchesFarm;
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Certificate and Document Verification</h1>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-8">
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700">Total Certificates</h3>
          <p className="text-3xl font-bold text-blue-600">{stats.totalCertificates}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700">Active Certificates</h3>
          <p className="text-3xl font-bold text-green-600">{stats.activeCertificates}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700">Expired Certificates</h3>
          <p className="text-3xl font-bold text-red-600">{stats.expiredCertificates}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700">Signed Documents</h3>
          <p className="text-3xl font-bold text-purple-600">{stats.documentsWithSignatures}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700">Blockchain Verified</h3>
          <p className="text-3xl font-bold text-amber-600">{stats.documentsWithBlockchain}</p>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <p>{successMessage}</p>
        </div>
      )}

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Digital Certificates</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200">
            <thead>
              <tr className="bg-gray-100">
                <th className="py-2 px-4 border-b text-left">Name</th>
                <th className="py-2 px-4 border-b text-left">Issuer</th>
                <th className="py-2 px-4 border-b text-left">Subject</th>
                <th className="py-2 px-4 border-b text-left">Valid From</th>
                <th className="py-2 px-4 border-b text-left">Valid To</th>
                <th className="py-2 px-4 border-b text-left">Farm</th>
                <th className="py-2 px-4 border-b text-left">Status</th>
              </tr>
            </thead>
            <tbody>
              {certificates.length === 0 ? (
                <tr>
                  <td colSpan={7} className="py-4 px-4 text-center text-gray-500">
                    No certificates found
                  </td>
                </tr>
              ) : (
                certificates.map(cert => (
                  <tr key={cert.id} className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b">{cert.certificate_name}</td>
                    <td className="py-2 px-4 border-b">{cert.issuer}</td>
                    <td className="py-2 px-4 border-b">{cert.subject}</td>
                    <td className="py-2 px-4 border-b">{new Date(cert.valid_from).toLocaleDateString()}</td>
                    <td className="py-2 px-4 border-b">{new Date(cert.valid_to).toLocaleDateString()}</td>
                    <td className="py-2 px-4 border-b">{cert.farm?.name || 'Unknown'}</td>
                    <td className="py-2 px-4 border-b">
                      <span className={`px-2 py-1 rounded text-xs ${cert.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        {cert.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Documents with Digital Signatures</h2>

        <div className="flex flex-wrap gap-4 mb-4">
          <div className="w-full md:w-1/3">
            <label className="block text-sm font-medium text-gray-700 mb-1">Search Documents</label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by title..."
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>

          <div className="w-full md:w-1/3">
            <label className="block text-sm font-medium text-gray-700 mb-1">Filter by Farm</label>
            <select
              value={farmFilter}
              onChange={(e) => setFarmFilter(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded"
            >
              <option value="">All Farms</option>
              {farms.map(farm => (
                <option key={farm.id} value={farm.id}>{farm.name}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200">
            <thead>
              <tr className="bg-gray-100">
                <th className="py-2 px-4 border-b text-left">Title</th>
                <th className="py-2 px-4 border-b text-left">Type</th>
                <th className="py-2 px-4 border-b text-left">Status</th>
                <th className="py-2 px-4 border-b text-left">Created</th>
                <th className="py-2 px-4 border-b text-left">Digital Signature</th>
                <th className="py-2 px-4 border-b text-left">Blockchain</th>
                <th className="py-2 px-4 border-b text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredDocuments.length === 0 ? (
                <tr>
                  <td colSpan={7} className="py-4 px-4 text-center text-gray-500">
                    No documents found
                  </td>
                </tr>
              ) : (
                filteredDocuments.map(doc => (
                  <tr key={doc.id} className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b">{doc.title}</td>
                    <td className="py-2 px-4 border-b">{doc.document_type}</td>
                    <td className="py-2 px-4 border-b">
                      <span className={`px-2 py-1 rounded text-xs ${
                        doc.status === 'completed' ? 'bg-green-100 text-green-800' : 
                        doc.status === 'draft' ? 'bg-gray-100 text-gray-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {doc.status}
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b">{new Date(doc.created_at).toLocaleDateString()}</td>
                    <td className="py-2 px-4 border-b">
                      {doc.has_digital_certificate_signature ? (
                        <span className="px-2 py-1 rounded text-xs bg-green-100 text-green-800">Yes</span>
                      ) : (
                        <span className="px-2 py-1 rounded text-xs bg-red-100 text-red-800">No</span>
                      )}
                    </td>
                    <td className="py-2 px-4 border-b">
                      {doc.has_blockchain_verification ? (
                        <span className="px-2 py-1 rounded text-xs bg-green-100 text-green-800">Yes</span>
                      ) : (
                        <span className="px-2 py-1 rounded text-xs bg-red-100 text-red-800">No</span>
                      )}
                    </td>
                    <td className="py-2 px-4 border-b">
                      <button
                        onClick={() => handleVerifyDocument(doc.id)}
                        disabled={verifying}
                        className="bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded text-sm"
                      >
                        {verifying && selectedDocument?.id === doc.id ? 'Verifying...' : 'Verify'}
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {verificationResult && selectedDocument && (
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold mb-4">Verification Results for "{selectedDocument.title}"</h3>

          <div className="mb-4">
            <h4 className="font-medium mb-2">Digital Signature Verification</h4>
            <div className={`p-4 rounded ${verificationResult.verified ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
              <p className={`font-semibold ${verificationResult.verified ? 'text-green-700' : 'text-red-700'}`}>
                {verificationResult.verified ? 'Signature Valid' : 'Signature Invalid'}
              </p>
              <p className="text-sm mt-1">{verificationResult.message}</p>
              {verificationResult.documentHash && (
                <div className="mt-2">
                  <p className="text-xs text-gray-600">Document Hash:</p>
                  <p className="text-xs font-mono bg-gray-100 p-2 rounded mt-1 break-all">{verificationResult.documentHash}</p>
                </div>
              )}
            </div>
          </div>

          {verificationResult.blockchainVerified !== undefined && (
            <div>
              <h4 className="font-medium mb-2">Blockchain Verification</h4>
              <div className={`p-4 rounded ${verificationResult.blockchainVerified ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                <p className={`font-semibold ${verificationResult.blockchainVerified ? 'text-green-700' : 'text-red-700'}`}>
                  {verificationResult.blockchainVerified ? 'Blockchain Verification Valid' : 'Blockchain Verification Invalid'}
                </p>
                <p className="text-sm mt-1">{verificationResult.blockchainMessage}</p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CertificateVerifier;
