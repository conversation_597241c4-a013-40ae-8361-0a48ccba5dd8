import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Dashboard from './Dashboard';
import FarmManagement from './FarmManagement';
import UserManagement from './UserManagement';
import RoleManagement from './RoleManagement';
import SubscriptionManagement from './SubscriptionManagement';
import PromoCodeManagement from './PromoCodeManagement';
import IntegrationManagement from './IntegrationManagement';
import SupplierManagement from './SupplierManagement';
import DatabaseMigrations from './DatabaseMigrations';
import DatabaseHealth from './DatabaseHealth';
import Logs from './Logs';
import EnvironmentVariables from './EnvironmentVariables';
import HelpTipManagement from './HelpTipManagement';
import HelpGuideManagement from './HelpGuideManagement';
import SupportTicketManagement from './SupportTicketManagement';
import FAQManagement from './FAQManagement';
import DashboardLayoutManagement from './DashboardLayoutManagement';
import ScriptExecutions from './ScriptExecutions';
import ApiEndpointManagement from './ApiEndpointManagement';
import ApiCacheManagement from './ApiCacheManagement';
import AIConfigurationManagement from './AIConfigurationManagement';
import ChatActivityMonitor from './ChatActivityMonitor';
import MessageModerationTools from './MessageModerationTools';
import MatrixUserManagement from './MatrixUserManagement';
import MatrixRoomManagement from './MatrixRoomManagement';
import MatrixServerStats from './MatrixServerStats';
import StripeDebugger from './StripeDebugger';
import CertificateVerifier from './CertificateVerifier';
import GlobalAdminLayout from '../../components/layouts/GlobalAdminLayout';
import { useAuth } from '../../context/AuthContext';

const GlobalAdminRoutes = () => {
  const { user } = useAuth();

  // Redirect if user is not a global admin
  if (!user || user.is_global_admin === false || user.is_global_admin === undefined) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <GlobalAdminLayout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/farms" element={<FarmManagement />} />
        <Route path="/users" element={<UserManagement />} />
        <Route path="/roles" element={<RoleManagement />} />
        <Route path="/subscriptions" element={<SubscriptionManagement />} />
        <Route path="/promo-codes" element={<PromoCodeManagement />} />
        <Route path="/integrations" element={<IntegrationManagement />} />
        <Route path="/suppliers" element={<SupplierManagement />} />
        <Route path="/db-migrations" element={<DatabaseMigrations />} />
        <Route path="/db-health" element={<DatabaseHealth />} />
        <Route path="/scripts" element={<ScriptExecutions />} />
        <Route path="/logs" element={<Logs />} />
        <Route path="/environment" element={<EnvironmentVariables />} />
        <Route path="/help-tips" element={<HelpTipManagement />} />
        <Route path="/help-guides" element={<HelpGuideManagement />} />
        <Route path="/support-tickets" element={<SupportTicketManagement />} />
        <Route path="/faqs" element={<FAQManagement />} />
        <Route path="/dashboard-layouts" element={<DashboardLayoutManagement />} />
        <Route path="/api-endpoints" element={<ApiEndpointManagement />} />
        <Route path="/api-cache" element={<ApiCacheManagement />} />
        <Route path="/ai-configuration" element={<AIConfigurationManagement />} />
        <Route path="/chat-activity" element={<ChatActivityMonitor />} />
        <Route path="/chat-moderation" element={<MessageModerationTools />} />
        <Route path="/matrix-users" element={<MatrixUserManagement />} />
        <Route path="/matrix-rooms" element={<MatrixRoomManagement />} />
        <Route path="/matrix-stats" element={<MatrixServerStats />} />
        <Route path="/stripe-debugger" element={<StripeDebugger />} />
        <Route path="/certificate-verifier" element={<CertificateVerifier />} />
        <Route path="*" element={<Navigate to="/admin" replace />} />
      </Routes>
    </GlobalAdminLayout>
  );
};

// Export the component
export default GlobalAdminRoutes;
